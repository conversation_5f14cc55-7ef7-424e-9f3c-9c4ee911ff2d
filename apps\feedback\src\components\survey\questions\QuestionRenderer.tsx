import React, { useState, useEffect } from 'react';
import { Button } from '@repo/ui/components/button';
import { Textarea } from '@repo/ui/components/textarea';
import { Input } from '@repo/ui/components/input';
import { RadioGroup, RadioGroupItem } from '@repo/ui/components/radio-group';
import { Checkbox } from '@repo/ui/components/checkbox';
import { Label } from '@repo/ui/components/label';
import { Edit3 } from 'lucide-react';
import { SurveyQuestion } from '@/services/engagementSurveyService';

// Extend SurveyQuestion to include text property for instructions
interface ExtendedSurveyQuestion extends SurveyQuestion {
  text?: string;
}

interface QuestionRendererProps {
  question: ExtendedSurveyQuestion;
  questionNumber: number;
  onResponseUpdate: (questionId: string, value: any, responseId?: string) => void;
  onCommentUpdate: (questionId: string, comment: string, commentId?: string) => void;
}

const QuestionRenderer: React.FC<QuestionRendererProps> = ({
  question,
  questionNumber,
  onResponseUpdate,
  onCommentUpdate
}) => {
  const [response, setResponse] = useState(question.response?.value || '');
  const [feedback, setFeedback] = useState('');
  const [showFeedback, setShowFeedback] = useState(false);

  useEffect(() => {
    setResponse(question.response?.value || '');
  }, [question.response?.value]);

  const handleResponseChange = (newResponse: any) => {
    setResponse(newResponse);
    onResponseUpdate(question.id, newResponse, question.response?.id);
  };

  const handleFeedbackChange = (newFeedback: string) => {
    setFeedback(newFeedback);
    onCommentUpdate(question.id, newFeedback);
  };

  const renderRatingScale = () => {
    // Get the options from the question or use default
    const options = question.options || {
      "1": "Scale - 1",
      "2": "Scale - 2",
      "3": "Scale - 3",
      "4": "Scale - 4",
      "5": "Scale - 5"
    };

    // Create ratings array from options, plus "No basis" option
    const ratings = [
      { value: 5, label: options["5"] || 'Scale - 5' },
      { value: 4, label: options["4"] || 'Scale - 4' },
      { value: 3, label: options["3"] || 'Scale - 3' },
      { value: 2, label: options["2"] || 'Scale - 2' },
      { value: 1, label: options["1"] || 'Scale - 1' },
      { value: 0, label: 'No basis for rating' }
    ];

    // If reverse scale is enabled, reverse the order (but keep "No basis" at the end)
    const displayRatings = question.is_reverse_scale ?
      [...ratings.slice(0, -1).reverse(), ratings[ratings.length - 1]] :
      ratings;

    return (
      <div className="mt-4">
        <div className="grid grid-cols-6 gap-3 w-full">
          {displayRatings.map((rating) => (
            <div key={rating.value} className="flex flex-col">
              <button
                type="button"
                onClick={() => handleResponseChange(rating.value)}
                className={`w-full h-auto p-0 border rounded transition-colors ${
                  response === rating.value
                    ? 'bg-blue-500 text-white border-blue-500'
                    : 'bg-white text-gray-700 hover:bg-gray-50 border-gray-300'
                }`}
              >
                <div className="flex items-center w-full">
                  <div className="flex items-center justify-center min-w-[30px] py-2 px-2 font-medium">
                    {rating.value}
                  </div>
                  <div className="border-l border-gray-300 px-3 py-2 flex-1 text-left text-sm">
                    {rating.value === 0 ? 'No basis for rating' : rating.label}
                  </div>
                </div>
              </button>
            </div>
          ))}
        </div>
      </div>
    );
  };

  const renderRadioOptions = () => {
    if (!question.options || !Array.isArray(question.options)) return null;

    return (
      <RadioGroup
        value={response?.toString()}
        onValueChange={(value) => handleResponseChange(value)}
        className="space-y-3"
      >
        {question.options.map((option, index) => (
          <div key={option.id || index} className="flex items-center space-x-3">
            <RadioGroupItem 
              value={option.value?.toString() || option.text || option} 
              id={`option-${option.id || index}`} 
            />
            <Label htmlFor={`option-${option.id || index}`} className="cursor-pointer">
              {option.text || option.label || option}
            </Label>
          </div>
        ))}
      </RadioGroup>
    );
  };

  const renderCheckboxOptions = () => {
    if (!question.options || !Array.isArray(question.options)) return null;

    const selectedValues = Array.isArray(response) ? response : [];

    return (
      <div className="space-y-3">
        {question.options.map((option, index) => (
          <div key={option.id || index} className="flex items-center space-x-3">
            <Checkbox
              id={`checkbox-${option.id || index}`}
              checked={selectedValues.includes(option.value || option.text || option)}
              onCheckedChange={(checked) => {
                const optionValue = option.value || option.text || option;
                const newValues = checked
                  ? [...selectedValues, optionValue]
                  : selectedValues.filter(v => v !== optionValue);
                handleResponseChange(newValues);
              }}
            />
            <Label htmlFor={`checkbox-${option.id || index}`} className="cursor-pointer">
              {option.text || option.label || option}
            </Label>
          </div>
        ))}
      </div>
    );
  };



  const renderInput = () => (
    <Input
      value={response}
      onChange={(e) => handleResponseChange(e.target.value)}
      placeholder="Enter your response..."
    />
  );

  const renderInstruction = () => (
    <div className="p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg">
      <p className="text-gray-700 dark:text-gray-300 text-sm leading-relaxed">
        {question.text}
      </p>
    </div>
  );

  const renderQuestionContent = () => {
    switch (question.resourcetype || question.type) {
      case 'QuestionRating':
        return renderRatingScale();
      case 'QuestionRadio':
        return renderRadioOptions();
      case 'QuestionCheckbox':
        return renderCheckboxOptions();
      case 'QuestionInput':
        return renderInput();
      case 'Paragraph':
        // Check if this is an instruction paragraph
        // Instructions typically have text content but no input capability
        // Based on legacy app: instructions have text but no options and aren't meant for user input
        const isInstructionParagraph = question.text &&
          question.text.trim().length > 0

        return isInstructionParagraph && renderInstruction();
      case 'Instruction':
      case 'QuestionInstruction':
      case 'InstructionText':
      case 'Text':
        return renderInstruction();
      default:
        return (
          <div className="p-4 border border-dashed rounded-lg text-center text-muted-foreground">
            Question type "{question.resourcetype || question.type}" not yet implemented
          </div>
        );
    }
  };

  // For instruction questions, render differently (no question number, no feedback)
  const questionType = question.resourcetype || question.type;
  const isInstructionQuestion = ['Instruction', 'QuestionInstruction', 'InstructionText', 'Text'].includes(questionType) ||
    (questionType === 'Paragraph' && question.text && question.text.trim().length > 0 && !question.options && !question.mandatory);

  if (isInstructionQuestion) {
    return (
      <div className="mb-6">
        {renderQuestionContent()}
      </div>
    );
  }

  return (
    <div className="mb-6 border rounded-lg p-4 border-gray-200 dark:border-gray-700">
      {/* Question Header */}
      <div className="mb-4 flex items-center justify-between">
        <h3 className="text-base font-medium text-gray-800 dark:text-white">
          {questionNumber}. {question.title || question.label}
          {question.mandatory && (
            <span className="text-red-500 dark:text-red-400 text-sm ml-1">*</span>
          )}
        </h3>
        {question.collect_feedback && (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowFeedback(!showFeedback)}
            className="text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20 flex items-center gap-1"
          >
            <Edit3 className="h-4 w-4" />
            Comment
          </Button>
        )}
      </div>

      {/* Question Content */}
      <div className="mb-4">
        {renderQuestionContent()}
      </div>

      {/* Feedback Section */}
      {question.collect_feedback && showFeedback && (
        <div className="mt-4">
          <div className="space-y-2">
            <Label htmlFor={`feedback-${question.id}`} className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Additional Comments (Optional)
            </Label>
            <Textarea
              id={`feedback-${question.id}`}
              value={feedback}
              onChange={(e) => handleFeedbackChange(e.target.value)}
              placeholder="Share any additional thoughts or feedback..."
              className="min-h-[80px] border-gray-300 dark:border-gray-600"
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default QuestionRenderer;
