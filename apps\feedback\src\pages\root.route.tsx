import useSession from "@/unmatched/modules/session/hook";
import { useEffect } from "react";
import { useNavigate } from "react-router";

const RootPath = [
  {
    path: "/",
    element: <Root />,
  },
];

function Root() {
  const { user } = useSession();
  const navigate = useNavigate();
  useEffect(() => {

    if (user.role === "ADMIN") {
      navigate("/admin");
    } else {
      navigate("/user");
    }
    //eslint-disable-next-line
  }, [user]);

  return <>Loading...</>;
}
export default RootPath;
