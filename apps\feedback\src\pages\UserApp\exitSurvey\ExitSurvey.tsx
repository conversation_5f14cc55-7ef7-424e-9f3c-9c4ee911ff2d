import React from 'react';
import { useParams } from 'react-router';
import { Alert, AlertDescription } from '@repo/ui/components/alert';
import SurveySidebar from './components/SurveySidebar';
import SurveyContent from './components/SurveyContent';
import SubmitConfirmation from './components/SubmitConfirmation';
import { useSurveyData } from './hooks/useSurveyData';
import { useSurveyNavigation } from './hooks/useSurveyNavigation';
import { useSurveyAutoSave } from './hooks/useSurveyAutoSave';

const ExitSurvey: React.FC = () => {
  const { id } = useParams<{ id: string }>();

  // Custom hooks for data management
  const {
    isLoading,
    surveyData,
    currentSection,
    error,
    setCurrentSection,
    setError,
    fetchSurveyData,
    setSurveyData
  } = useSurveyData();

  // Custom hooks for auto-save functionality
  const {
    isSaving,
    lastSaved,
    handleResponseUpdate,
    handleCommentUpdate
  } = useSurveyAutoSave(surveyData, setSurveyData);

  // Custom hooks for navigation
  const {
    confirmSubmit,
    isSubmitting,
    setConfirmSubmit,
    handleNext,
    handleSubmit,
    handleSectionSelect
  } = useSurveyNavigation();

  // Initialize survey data
  React.useEffect(() => {
    if (id) {
      fetchSurveyData(id);
    }
  }, [id, fetchSurveyData]);

  // Handle response updates with optimistic updates
  const onResponseUpdate = (questionId: string, value: any, responseId?: string) => {
    if (surveyData) {
      handleResponseUpdate(surveyData.responseId, questionId, value, responseId);
    }
  };

  // Handle comment updates with auto-save
  const onCommentUpdate = (questionId: string, comment: string, commentId?: string) => {
    if (surveyData) {
      handleCommentUpdate(surveyData.responseId, questionId, comment, commentId);
    }
  };

  // Handle navigation actions
  const onNext = () => handleNext(currentSection, surveyData, setCurrentSection);
  const onSectionSelect = (section: any) => handleSectionSelect(section, setCurrentSection);
  const onSubmit = async () => {
    try {
      await handleSubmit(surveyData);
    } catch (err) {
      setError('Failed to submit survey');
    }
  };

  if (error) {
    return (
      <div className="p-6 bg-white dark:bg-gray-900">
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      </div>
    );
  }

  if (confirmSubmit) {
    return (
      <SubmitConfirmation
        isSubmitting={isSubmitting}
        onBack={() => setConfirmSubmit(false)}
        onSubmit={onSubmit}
      />
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 w-full">
      <div className="flex w-full">
        {/* Sidebar */}
        <SurveySidebar
          isLoading={isLoading}
          surveyData={surveyData}
          currentSection={currentSection}
          onSectionSelect={onSectionSelect}
        />

        {/* Main Content */}
        <SurveyContent
          isLoading={isLoading}
          isSaving={isSaving}
          surveyData={surveyData}
          currentSection={currentSection}
          lastSaved={lastSaved}
          onResponseUpdate={onResponseUpdate}
          onCommentUpdate={onCommentUpdate}
          onNext={onNext}
        />
      </div>
    </div>
  );
};

export default ExitSurvey;
