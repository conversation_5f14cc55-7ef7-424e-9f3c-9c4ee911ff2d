import { useState } from 'react';
import { useNavigate } from 'react-router';
import { useSurveys, useSurveyStatus } from '../../../hooks/useSurveys';
import { SurveysSection } from '../../../components/survey/SurveysSection';
import { USER_ROUTES } from '@/app.routes';

function SurveyList() {
  const [page, _setPage] = useState(1);
  const pageSize = 10;
  const navigate = useNavigate();

  const {
    surveys,
    statsMap,
    loading,
    error
  } = useSurveys(page, pageSize);

  const { isOngoing } = useSurveyStatus();

  const handleViewDetails = (surveyId: string) => {
    // Navigate to terms page as in legacy app
    navigate(USER_ROUTES().dashboard.getSurveyTermsUrl(surveyId));
  };
  
  const ongoingSurveys = surveys.filter(isOngoing);
  const upcomingSurveys = surveys.filter(survey => !isOngoing(survey));

  if (error) {
    return (
      <div className="flex min-h-screen w-full items-center justify-center text-red-500 dark:text-red-400">
        {error}
      </div>
    );
  }


  return (
    <div className="w-full p-6 bg-white dark:bg-gray-900">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-semibold text-gray-900 dark:text-white">Surveys</h1>
      </div>
      
      <SurveysSection
        title="On-Going"
        surveys={ongoingSurveys}
        statsMap={statsMap}
        loading={loading && ongoingSurveys.length === 0}
        isOngoing={isOngoing}
        onViewDetails={handleViewDetails}
        emptyMessage="No ongoing surveys available."
      />
      
      {/* <SurveysSection
        title="Upcoming"
        surveys={upcomingSurveys}
        statsMap={statsMap}
        loading={loading && upcomingSurveys.length === 0}
        isOngoing={isOngoing}
        onViewDetails={handleViewDetails}
        emptyMessage="No upcoming surveys available."
      /> */}
    </div>
  );
}

export default SurveyList;
