import { useState, useCallback } from 'react';
import {
  saveQuestionResponse,
  saveCommentResponse,
  type EngagementSurveyData
} from '@/services/engagementSurveyService';

export const useSurveyAutoSave = (
  surveyData: EngagementSurveyData | null,
  setSurveyData: (data: EngagementSurveyData | null) => void
) => {
  const [isSaving, setIsSaving] = useState(false);
  const [saveTimeout, setSaveTimeout] = useState<ReturnType<typeof setTimeout> | null>(null);
  const [lastSaved, setLastSaved] = useState<Date>(new Date());

  // Auto-save functionality with debouncing
  const debouncedSave = useCallback((saveFunction: () => Promise<void>) => {
    if (saveTimeout) {
      clearTimeout(saveTimeout);
    }

    setIsSaving(true);

    const timeout = setTimeout(async () => {
      try {
        await saveFunction();
        setLastSaved(new Date());
      } catch (err) {
        console.error('Error saving response:', err);
      } finally {
        setIsSaving(false);
      }
    }, 1000); // 1 second debounce

    setSaveTimeout(timeout);
  }, [saveTimeout]);

  const handleResponseUpdate = useCallback((
    responseId: string,
    questionId: string,
    value: any,
    existingResponseId?: string,
    onComplete?: () => void
  ) => {
    // Optimistic update - update local state immediately
    if (surveyData) {
      const updatedSurveyData = { ...surveyData };
      let wasAnswered = false;
      let isNowAnswered = false;

      // Update the question response in all sections
      updatedSurveyData.sections = updatedSurveyData.sections.map(section => ({
        ...section,
        questions: section.questions.map(question => {
          if (question.id === questionId) {
            wasAnswered = question.response?.value !== undefined &&
                         question.response?.value !== null &&
                         question.response?.value !== '';
            isNowAnswered = value !== undefined && value !== null && value !== '';

            return {
              ...question,
              response: {
                id: question.response?.id || existingResponseId || '',
                value: value,
                is_valid: true
              }
            };
          }
          return question;
        })
      }));

      // Update questions array as well
      updatedSurveyData.questions = updatedSurveyData.questions.map(question => {
        if (question.id === questionId) {
          return {
            ...question,
            response: {
              id: question.response?.id || existingResponseId || '',
              value: value,
              is_valid: true
            }
          };
        }
        return question;
      });

      // Update completion percentage optimistically
      const totalQuestions = updatedSurveyData.questions.length;
      const answeredQuestions = updatedSurveyData.questions.filter(q =>
        q.response?.value !== undefined && q.response?.value !== null && q.response?.value !== ''
      ).length;
      updatedSurveyData.completion = totalQuestions > 0 ? Math.round((answeredQuestions / totalQuestions) * 100) : 0;

      setSurveyData(updatedSurveyData);
    }

    debouncedSave(async () => {
      await saveQuestionResponse(responseId, questionId, value, existingResponseId);
      if (onComplete) {
        onComplete();
      }
    });
  }, [debouncedSave, surveyData, setSurveyData]);

  const handleCommentUpdate = useCallback((
    responseId: string,
    questionId: string,
    comment: string,
    commentId?: string
  ) => {
    debouncedSave(async () => {
      await saveCommentResponse(responseId, questionId, comment, commentId);
    });
  }, [debouncedSave]);

  return {
    isSaving,
    lastSaved,
    handleResponseUpdate,
    handleCommentUpdate
  };
};
