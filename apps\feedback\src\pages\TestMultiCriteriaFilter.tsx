import React, { useState } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@repo/ui/components/card';
import MultiCriteriaFilter from '@/components/ui/MultiCriteriaFilter';

// Sample metadata matching your requirements
const sampleMetadata = {
  labels: [
    { field: "class_year", display_name: "Class Year", type: "str" },
    { field: "department", display_name: "Department", type: "str" },
    { field: "current_level", display_name: "Title", type: "str" },
    { field: "practice_group", display_name: "Practice Group", type: "str" },
    { field: "office_location", display_name: "Location", type: "str" }
  ],
  values: {
    class_year: ["2022", "2018", "2013", "2024", "2007", "2012"],
    department: ["Litigation", "Corporate", "Banking", "dsdsd"],
    current_level: ["Associate", "Counsel", "Partner"],
    practice_group: ["Litigation", "Corporate", "Banking", "IP"],
    office_location: ["New York", "Chicago", "San Francisco", "Dallas"]
  }
};

const TestMultiCriteriaFilter: React.FC = () => {
  const [filters, setFilters] = useState<Record<string, string[]>>({});

  const handleFiltersChange = (newFilters: Record<string, string[]>) => {
    setFilters(newFilters);
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="space-y-2">
        <h1 className="text-3xl font-bold">Multi-Criteria Filter Test</h1>
        <p className="text-muted-foreground">
          Test the dynamic multi-criteria filter component with sample data.
        </p>
      </div>

      <Card className='py-2 bg-transparent rounded-lg'>
        <CardContent className="px-2 space-y-4">
          <MultiCriteriaFilter
            metadata={sampleMetadata}
            filters={filters}
            onFiltersChange={handleFiltersChange}
          />
          
          {/* <div className="flex items-center gap-2">
            <Button variant="outline" onClick={handleReset}>
              Reset All Filters
            </Button>
            <span className="text-sm text-muted-foreground">
              {Object.keys(filters).length} filter(s) applied
            </span>
          </div> */}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Current Filter State</CardTitle>
        </CardHeader>
        <CardContent>
          <pre className="bg-muted p-4 rounded-md text-sm overflow-x-auto">
            {JSON.stringify(filters, null, 2)}
          </pre>
        </CardContent>
      </Card>
    </div>
  );
};

export default TestMultiCriteriaFilter;
